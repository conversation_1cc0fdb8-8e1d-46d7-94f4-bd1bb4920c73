# 蓝牙扫描流程完整分析文档

## 1. 项目架构概述

### 1.1 设计模式
本项目采用了多种设计模式来实现蓝牙扫描功能：

- **策略模式**: 不同的扫描器实现(`LeLollipopScanner`, `LeJBScanner`, `ClassicScanner`)
- **工厂模式**: `LeScannerCompat`根据Android版本创建合适的扫描器
- **模板方法模式**: `BaseScanner`定义扫描流程框架
- **观察者模式**: `ScanCallback`接口实现扫描事件通知

### 1.2 核心组件关系图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   应用层        │    │   扫描管理层      │    │   系统API层     │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ FileListActivity│───▶│ LeScannerCompat  │───▶│ BluetoothAdapter│
│ MainActivity    │    │ (工厂类)         │    │ BluetoothManager│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   BaseScanner    │
                    │   (抽象基类)     │
                    └──────────────────┘
                              │
                    ┌─────────┼─────────┐
                    ▼         ▼         ▼
            ┌──────────┐ ┌──────────┐ ┌──────────┐
            │LeLollipop│ │ LeJBScan │ │ Classic  │
            │Scanner   │ │ ner      │ │Scanner   │
            │(API 21+) │ │(API 18-20│ │(经典蓝牙)│
            └──────────┘ └──────────┘ └──────────┘
```

## 2. 核心组件详解

### 2.1 接口和抽象类

#### BtScanner 接口
```java
public interface BtScanner {
    void startScan(ScanCallback callback);  // 开始扫描
    void stopScan();                        // 停止扫描  
    void close();                           // 释放资源
}
```

#### ScanCallback 回调接口
```java
public interface ScanCallback {
    void onFound(BluetoothDevice device, int rssi, byte[] scanRecord);  // 发现设备
    void onScanStart();                                                 // 扫描开始
    void onScanFinish();                                               // 扫描结束
}
```

#### BaseScanner 抽象基类
- 管理蓝牙适配器实例
- 统一的回调处理机制
- 扫描状态跟踪
- 为子类提供通用功能

### 2.2 具体扫描器实现

#### LeLollipopScanner (Android 5.0+)
- 使用 `BluetoothLeScanner` API
- 支持 `ScanSettings` 配置
- 提供扫描失败回调
- 更好的电池优化

#### LeJBScanner (Android 4.3-4.4)  
- 使用 `BluetoothAdapter.startLeScan()` API
- 兼容旧版本设备
- 功能相对简单但稳定

#### ClassicScanner (经典蓝牙)
- 使用 `BluetoothAdapter.startDiscovery()` API  
- 通过广播接收器监听扫描事件
- 适用于传统蓝牙设备

## 3. 完整扫描流程时序图

### 3.1 BLE扫描流程 (Android 5.0+)

```
用户操作    应用层           扫描管理层        系统API层        回调处理
   │          │                │               │               │
   │─点击扫描─▶│                │               │               │
   │          │─检查权限────────▶│               │               │
   │          │                │               │               │
   │          │─创建扫描器──────▶│               │               │
   │          │                │─获取适配器───▶│               │
   │          │                │◀─返回适配器───│               │
   │          │                │               │               │
   │          │─开始扫描────────▶│               │               │
   │          │                │─startScan────▶│               │
   │          │                │               │─扫描开始─────▶│
   │          │◀─扫描开始回调───│               │               │
   │          │                │               │               │
   │          │                │               │─发现设备─────▶│
   │          │◀─设备发现回调───│               │               │
   │          │                │               │               │
   │          │─停止扫描────────▶│               │               │
   │          │                │─stopScan─────▶│               │
   │          │                │               │─扫描结束─────▶│
   │          │◀─扫描结束回调───│               │               │
```

### 3.2 经典蓝牙扫描流程

```
用户操作    应用层           扫描管理层        广播接收器        系统API层
   │          │                │               │               │
   │─点击扫描─▶│                │               │               │
   │          │─注册广播接收器─▶│               │               │
   │          │                │─注册监听─────▶│               │
   │          │                │               │               │
   │          │─开始扫描────────▶│               │               │
   │          │                │─startDiscovery▶│               │
   │          │                │               │◀─扫描开始广播─│
   │          │◀─扫描开始回调───│◀─处理广播────│               │
   │          │                │               │               │
   │          │                │               │◀─发现设备广播─│
   │          │◀─设备发现回调───│◀─处理广播────│               │
   │          │                │               │               │
   │          │                │               │◀─扫描结束广播─│
   │          │◀─扫描结束回调───│◀─处理广播────│               │
```

## 4. 版本兼容性处理

### 4.1 Android版本差异

| Android版本 | API Level | 使用的扫描器 | 主要API |
|------------|-----------|-------------|---------|
| 4.2及以下   | ≤17       | 不支持BLE   | N/A |
| 4.3-4.4    | 18-20     | LeJBScanner | BluetoothAdapter.startLeScan() |
| 5.0+       | 21+       | LeLollipopScanner | BluetoothLeScanner.startScan() |

### 4.2 兼容性工厂类

```java
public static BtScanner getLeScanner(Context context) {
    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
        return new LeJBScanner(context);  // 旧版API
    } else {
        return new LeLollipopScanner(context);  // 新版API
    }
}
```

## 5. 权限管理流程

### 5.1 权限分类

#### Android 12以下版本所需权限
```xml
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

#### Android 12及以上版本新增权限
```xml
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
```

### 5.2 权限检查流程

```
开始扫描
    │
    ▼
检查Android版本
    │
    ├─ Android 12+ ─────┐
    │                   ▼
    │              检查BLUETOOTH_SCAN
    │                   │
    │                   ▼
    │              检查BLUETOOTH_CONNECT
    │                   │
    └─ Android 12以下 ──┼─────▶ 检查ACCESS_COARSE_LOCATION
                        │
                        ▼
                   权限检查结果
                        │
                ┌───────┼───────┐
                ▼               ▼
            权限完整         权限缺失
                │               │
                ▼               ▼
            开始扫描        请求权限
                                │
                                ▼
                           用户授权结果
                                │
                        ┌───────┼───────┐
                        ▼               ▼
                    授权成功         授权失败
                        │               │
                        ▼               ▼
                    开始扫描         显示错误
```

### 5.3 权限请求代码示例

```java
private void checkAndRequestPermissions() {
    List<String> permissionsNeeded = new ArrayList<>();

    // 基础位置权限
    if (ContextCompat.checkSelfPermission(this, ACCESS_COARSE_LOCATION)
            != PackageManager.PERMISSION_GRANTED) {
        permissionsNeeded.add(ACCESS_COARSE_LOCATION);
    }

    // Android 12+特殊权限
    if (Build.VERSION.SDK_INT >= ANDROID_12_SDK) {
        if (ContextCompat.checkSelfPermission(this, BLUETOOTH_SCAN)
                != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(BLUETOOTH_SCAN);
        }
        if (ContextCompat.checkSelfPermission(this, BLUETOOTH_CONNECT)
                != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(BLUETOOTH_CONNECT);
        }
    }

    if (!permissionsNeeded.isEmpty()) {
        ActivityCompat.requestPermissions(this,
            permissionsNeeded.toArray(new String[0]),
            REQUEST_BLUETOOTH_PERMISSIONS);
    } else {
        startScan();
    }
}
```

## 6. 异常处理机制

### 6.1 常见异常类型

#### 权限异常
- **SecurityException**: 缺少必要的蓝牙权限
- **处理方式**: 捕获异常，提示用户授权

#### 蓝牙状态异常
- **蓝牙未开启**: BluetoothAdapter为null或未启用
- **处理方式**: 检查蓝牙状态，引导用户开启

#### 扫描失败异常
- **扫描器不可用**: BluetoothLeScanner为null
- **扫描启动失败**: startScan返回false
- **处理方式**: 重新获取扫描器，重试或降级处理

### 6.2 异常处理流程

```
扫描操作
    │
    ▼
try {
    执行扫描
} catch {
    │
    ├─ SecurityException ────▶ 权限不足处理
    │                           │
    │                           ▼
    │                      提示用户授权
    │
    ├─ IllegalStateException ─▶ 蓝牙状态异常
    │                           │
    │                           ▼
    │                      检查并开启蓝牙
    │
    └─ Exception ──────────────▶ 通用异常处理
                                │
                                ▼
                           记录日志并重试
}
```

### 6.3 错误恢复策略

#### 自动重试机制
```java
private void startScanWithRetry(int retryCount) {
    try {
        mLeScanner.startScan(null, scanSettings, mLeScanCallBack);
    } catch (SecurityException e) {
        Log.e(TAG, "权限错误: " + e.getMessage());
        // 不重试，直接提示用户
        showPermissionError();
    } catch (Exception e) {
        if (retryCount > 0) {
            // 延迟重试
            handler.postDelayed(() -> startScanWithRetry(retryCount - 1), 1000);
        } else {
            // 重试次数用完，显示错误
            showScanError();
        }
    }
}
```

#### 降级处理
```java
private void fallbackScan() {
    if (mLeScanner == null) {
        // 重新获取扫描器
        mLeScanner = mBluetoothAdapter.getBluetoothLeScanner();
    }

    if (mLeScanner == null) {
        // 降级到经典蓝牙扫描
        ClassicScanner classicScanner = new ClassicScanner(this);
        classicScanner.startScan(mScanCallback);
    }
}
```

## 7. 扫描配置优化

### 7.1 扫描参数配置

#### Android 8.0+高级配置

```java
ScanSettings scanSettings = new ScanSettings.Builder()
    .setScanMode(SCAN_MODE_LOW_LATENCY)           // 低延迟模式
    .setLegacy(false)                             // 使用新API
    .setPhy(ScanSettings.PHY_LE_ALL_SUPPORTED)   // 支持所有物理层
    .setNumOfMatches(MATCH_NUM_MAX_ADVERTISEMENT) // 最大匹配数
    .setCallbackType(CALLBACK_TYPE_ALL_MATCHES)   // 所有匹配回调
    .setMatchMode(MATCH_MODE_AGGRESSIVE)          // 激进匹配
    .build();
```

#### 扫描模式对比

| 扫描模式 | 功耗 | 发现速度 | 适用场景 |
|---------|------|----------|----------|
| LOW_POWER | 低 | 慢 | 后台长时间扫描 |
| BALANCED | 中 | 中 | 一般应用场景 |
| LOW_LATENCY | 高 | 快 | 快速发现设备 |

### 7.2 扫描过滤器

```java
// 按设备名称过滤
ScanFilter nameFilter = new ScanFilter.Builder()
    .setDeviceName("BES_DEVICE")
    .build();

// 按服务UUID过滤
ScanFilter serviceFilter = new ScanFilter.Builder()
    .setServiceUuid(ParcelUuid.fromString("0000180F-0000-1000-8000-00805F9B34FB"))
    .build();

List<ScanFilter> filters = Arrays.asList(nameFilter, serviceFilter);
mLeScanner.startScan(filters, scanSettings, mLeScanCallBack);
```

## 8. 最佳实践建议

### 8.1 性能优化

#### 扫描时间控制
```java
// 设置扫描超时
private static final int SCAN_TIMEOUT = 10000; // 10秒

private void startScanWithTimeout() {
    startScan();

    // 设置超时停止
    handler.postDelayed(() -> {
        stopScan();
        onScanTimeout();
    }, SCAN_TIMEOUT);
}
```

#### 避免频繁扫描
```java
private long lastScanTime = 0;
private static final long MIN_SCAN_INTERVAL = 5000; // 5秒间隔

public void startScan() {
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastScanTime < MIN_SCAN_INTERVAL) {
        Log.w(TAG, "扫描间隔太短，忽略本次请求");
        return;
    }
    lastScanTime = currentTime;
    // 执行扫描...
}
```

### 8.2 资源管理

#### 生命周期管理
```java
@Override
protected void onResume() {
    super.onResume();
    // 恢复扫描
    if (shouldAutoScan) {
        startScan();
    }
}

@Override
protected void onPause() {
    super.onPause();
    // 暂停扫描以节省电量
    stopScan();
}

@Override
protected void onDestroy() {
    super.onDestroy();
    // 释放扫描器资源
    if (scanner != null) {
        scanner.close();
        scanner = null;
    }
}
```

#### 内存泄漏防护
```java
// 使用弱引用避免内存泄漏
private static class ScanHandler extends Handler {
    private final WeakReference<Activity> activityRef;

    ScanHandler(Activity activity) {
        this.activityRef = new WeakReference<>(activity);
    }

    @Override
    public void handleMessage(Message msg) {
        Activity activity = activityRef.get();
        if (activity != null && !activity.isFinishing()) {
            // 处理扫描结果
        }
    }
}
```

### 8.3 用户体验优化

#### 扫描状态指示
```java
private void updateScanUI(boolean scanning) {
    if (scanning) {
        progressBar.setVisibility(View.VISIBLE);
        scanButton.setText("停止扫描");
        scanButton.setEnabled(true);
    } else {
        progressBar.setVisibility(View.GONE);
        scanButton.setText("开始扫描");
        scanButton.setEnabled(true);
    }
}
```

#### 设备列表管理
```java
private void addDeviceToList(BluetoothDevice device, int rssi) {
    // 避免重复添加
    for (DeviceInfo info : deviceList) {
        if (info.device.getAddress().equals(device.getAddress())) {
            // 更新RSSI值
            info.rssi = rssi;
            adapter.notifyDataSetChanged();
            return;
        }
    }

    // 添加新设备
    deviceList.add(new DeviceInfo(device, rssi));
    adapter.notifyItemInserted(deviceList.size() - 1);
}
```

## 9. 总结

### 9.1 架构优势
- **模块化设计**: 各组件职责清晰，易于维护
- **版本兼容**: 自动适配不同Android版本
- **异常处理**: 完善的错误处理和恢复机制
- **扩展性强**: 易于添加新的扫描器类型

### 9.2 关键要点
1. **权限管理**: 根据Android版本动态请求权限
2. **版本适配**: 使用工厂模式选择合适的扫描器
3. **异常处理**: 完善的try-catch和重试机制
4. **资源管理**: 及时释放扫描器和回调资源
5. **性能优化**: 控制扫描频率和时长

### 9.3 注意事项
- 扫描前必须检查并获取必要权限
- 不同Android版本需要不同的权限和API
- 扫描过程中要处理各种异常情况
- 及时停止扫描以节省电量
- 避免内存泄漏和资源占用

这个蓝牙扫描架构为开发者提供了一个完整、可靠的蓝牙设备发现解决方案，通过合理的设计模式和完善的异常处理，确保了在各种Android设备上的稳定运行。
